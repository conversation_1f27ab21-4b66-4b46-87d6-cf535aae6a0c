# 🔧 TiniApp Troubleshooting Guide

## 🚨 Common Issues & Solutions

### 1. NODE_MODULE_VERSION Compatibility Issues

#### Problem
```
Error: The module 'better-sqlite3' was compiled against a different Node.js version using NODE_MODULE_VERSION 119. This version of Node.js requires NODE_MODULE_VERSION 127.
```

#### Root Cause
- better-sqlite3 native module compiled for wrong Node.js version
- Bun requires NODE_MODULE_VERSION 127 (Node.js 22.x)

#### Solution
```bash
# 1. Install Node.js 22.x
nvm install 22.14.0
nvm use 22.14.0
nvm alias default 22.14.0

# 2. Verify version
node --version  # Should be v22.14.0
node -e "console.log(process.versions.modules)"  # Should be 127

# 3. Clean and reinstall
rm -rf node_modules bun.lockb
bun install
npm rebuild better-sqlite3
```

### 2. Port Already in Use

#### Problem
```
Error: listen EADDRINUSE: address already in use :::3000
```

#### Solution
```bash
# Find and kill processes using port 3000
lsof -ti:3000 | xargs kill -9

# Or use a different port
PORT=3001 npm run dev
```

### 3. Electron Build Issues

#### Problem
```
ReferenceError: exports is not defined in ES module scope
```

#### Root Cause
- Electron main process compiled as ES module instead of CommonJS
- package.json has "type": "module" which affects all .js files

#### Solution
```bash
# Rebuild with correct extensions
npm run build:electron
npm run build:preload

# Verify files are .cjs
ls -la dist/electron/
# Should show: main.cjs, preload.cjs
```

### 4. Database Connection Errors

#### Problem
```
Error: Cannot open database
SQLITE_CANTOPEN: unable to open database file
```

#### Solutions
```bash
# 1. Check if data directory exists
mkdir -p data

# 2. Recreate database
rm -f data/app.db
npm run setup:db

# 3. Check permissions
chmod 755 data/
chmod 644 data/app.db
```

### 5. Authentication API Timeouts

#### Problem
- POST requests to /api/auth/* hang or timeout
- Registration/login not working

#### Root Cause
- Request body parsing issues
- Better Auth configuration problems

#### Solution
```bash
# 1. Check server logs for errors
npm run dev:server

# 2. Test with curl
curl -X POST http://localhost:3000/api/auth/sign-up/email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","name":"Test User"}'

# 3. Verify database tables
sqlite3 data/app.db ".tables"
# Should show: user, session, account, verification, etc.
```

### 6. Frontend Not Loading

#### Problem
- Blank page or 404 errors
- Static assets not found

#### Solutions
```bash
# 1. Check frontend directory
ls -la src/frontend/
ls -la dist/frontend/  # For production

# 2. Verify server is serving static files
curl http://localhost:3000/
curl http://localhost:3000/scripts/app.js

# 3. Check Content Security Policy
# Look for CSP errors in browser console
```

### 7. Theme Switching Not Working

#### Problem
- Theme selector not appearing
- Themes not applying

#### Solutions
```bash
# 1. Check DaisyUI CDN loading
# Open browser dev tools → Network tab
# Verify DaisyUI CSS loads successfully

# 2. Check JavaScript errors
# Open browser dev tools → Console tab
# Look for theme-related errors

# 3. Verify theme script
curl http://localhost:3000/scripts/themes.js
```

### 8. Hot Reload Not Working

#### Problem
- Changes not reflected automatically
- Server not restarting on file changes

#### Solutions
```bash
# 1. Check tsx is running
ps aux | grep tsx

# 2. Restart development server
npm run dev

# 3. Check file permissions
chmod -R 755 src/
```

## 🔍 Debugging Tools & Commands

### Environment Verification
```bash
# Check all versions
echo "Node.js: $(node --version)"
echo "npm: $(npm --version)"
echo "Bun: $(bun --version)"
echo "NODE_MODULE_VERSION: $(node -e 'console.log(process.versions.modules)')"

# Check platform
echo "Platform: $(uname -a)"
```

### Process Management
```bash
# List all Node.js processes
ps aux | grep node

# List processes using port 3000
lsof -i :3000

# Kill all Node.js processes (use with caution)
pkill -f node
```

### Database Debugging
```bash
# Connect to database
sqlite3 data/app.db

# List tables
.tables

# Check user table structure
.schema user

# Count records
SELECT COUNT(*) FROM user;

# Exit sqlite3
.quit
```

### Network Testing
```bash
# Test server connectivity
curl -I http://localhost:3000

# Test API endpoints
curl http://localhost:3000/api/health
curl http://localhost:3000/api/auth/get-session

# Test with verbose output
curl -v http://localhost:3000/api/health
```

### Log Analysis
```bash
# View server logs in real-time
npm run dev:server | tee server.log

# Search for errors
grep -i error server.log
grep -i "auth" server.log
```

## 🛠️ Advanced Troubleshooting

### Clean Installation
```bash
# Complete clean slate
rm -rf node_modules
rm -f package-lock.json bun.lockb
rm -rf dist/
rm -f data/app.db

# Clear caches
npm cache clean --force
bun pm cache rm

# Reinstall everything
bun install
npm run setup:db
npm run build
```

### Dependency Issues
```bash
# Check for conflicting dependencies
npm ls

# Audit for vulnerabilities
npm audit

# Update dependencies (careful!)
npm update
```

### Electron-Specific Issues
```bash
# Rebuild Electron native modules
npm run postinstall

# Clear Electron cache
rm -rf ~/.cache/electron

# Test Electron without app
npx electron --version
```

## 📊 Performance Debugging

### Memory Usage
```bash
# Monitor memory usage
top -p $(pgrep -f "node.*tsx")

# Node.js memory debugging
node --inspect src/server/index.ts
```

### Build Performance
```bash
# Time the build process
time npm run build

# Analyze bundle size
du -sh dist/
```

## 🔐 Security Debugging

### Check Security Headers
```bash
# Test security headers
curl -I http://localhost:3000

# Should include:
# X-Content-Type-Options: nosniff
# X-Frame-Options: DENY
# X-XSS-Protection: 1; mode=block
```

### Validate CSP
```bash
# Check Content Security Policy
curl -s http://localhost:3000 | grep -i "content-security-policy"
```

## 📞 Getting Help

### Before Asking for Help

1. **Check this troubleshooting guide**
2. **Search existing GitHub issues**
3. **Try the clean installation process**
4. **Gather system information**

### Information to Include

When reporting issues, include:

```bash
# System information
echo "OS: $(uname -a)"
echo "Node.js: $(node --version)"
echo "npm: $(npm --version)"
echo "Bun: $(bun --version)"
echo "NODE_MODULE_VERSION: $(node -e 'console.log(process.versions.modules)')"

# Error logs
npm run dev 2>&1 | head -50

# Package versions
npm ls better-sqlite3 better-auth electron
```

### Creating a Bug Report

Include:
1. **Environment details** (OS, Node.js version, etc.)
2. **Steps to reproduce** the issue
3. **Expected behavior**
4. **Actual behavior**
5. **Error messages** (full stack traces)
6. **Screenshots** (if UI-related)

## ✅ Quick Health Check

Run this command to verify everything is working:

```bash
# Health check script
echo "🔍 TiniApp Health Check"
echo "======================"
echo "Node.js: $(node --version)"
echo "Bun: $(bun --version)"
echo "NODE_MODULE_VERSION: $(node -e 'console.log(process.versions.modules)')"
echo ""
echo "📁 Files:"
ls -la data/app.db 2>/dev/null && echo "✅ Database exists" || echo "❌ Database missing"
ls -la dist/ 2>/dev/null && echo "✅ Build directory exists" || echo "❌ Build directory missing"
echo ""
echo "🌐 Server:"
curl -s http://localhost:3000/api/health >/dev/null && echo "✅ Server responding" || echo "❌ Server not responding"
echo ""
echo "Health check complete!"
```

---

**💡 Pro Tip**: Most issues can be resolved by ensuring you're using Node.js 22.x and running a clean installation. When in doubt, start with the clean installation process!
