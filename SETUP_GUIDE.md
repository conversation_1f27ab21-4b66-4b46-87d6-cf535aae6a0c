# 📚 TiniApp Complete Setup & Integration Guide

## 🎯 Overview

This guide provides comprehensive instructions for setting up, developing, and deploying TiniApp - a modern desktop application with full-stack authentication.

## ✅ Integration Testing Results

### API Integration Status: **FULLY FUNCTIONAL** ✅

All Better Auth endpoints have been tested and verified:

- ✅ **Health Check**: `/api/health` - Returns server status
- ✅ **Session Management**: `/api/auth/get-session` - Session retrieval
- ✅ **User Registration**: `/api/auth/sign-up/email` - Account creation
- ✅ **User Login**: `/api/auth/sign-in/email` - Authentication
- ✅ **User Logout**: `/api/auth/sign-out` - Session termination

### Frontend-Backend Communication: **VERIFIED** ✅

- ✅ CORS headers properly configured
- ✅ Content Security Policy allows required requests
- ✅ Authentication state management working
- ✅ Toast notifications displaying correctly
- ✅ Session persistence across page reloads

### Electron Integration: **OPERATIONAL** ✅

- ✅ API calls work correctly in Electron renderer
- ✅ Preload script properly configured
- ✅ Security policies enforced
- ✅ Desktop app launches successfully

## 🛠️ Prerequisites & Environment Setup

### Required Software Versions

**CRITICAL**: Use exact versions to avoid compatibility issues

1. **Node.js 22.x** (NODE_MODULE_VERSION 127)
   ```bash
   # Check current version
   node --version  # Must be v22.x.x
   node -e "console.log('NODE_MODULE_VERSION:', process.versions.modules)"  # Must be 127
   
   # Install via nvm (recommended)
   nvm install 22.14.0
   nvm use 22.14.0
   nvm alias default 22.14.0
   ```

2. **Bun Latest**
   ```bash
   # Install Bun
   curl -fsSL https://bun.sh/install | bash
   
   # Verify installation
   bun --version  # Should be v1.2.15+
   ```

### Why Node.js 22.x is Required

- **better-sqlite3** native module compatibility
- **NODE_MODULE_VERSION 127** matches Bun requirements
- **ES Module support** for modern JavaScript features

## 🚀 Quick Start (5 Minutes)

### 1. Clone & Install
```bash
# Clone repository
git clone <repository-url>
cd tiniapp

# Install dependencies (uses Bun for speed)
bun install

# Initialize database
npm run setup:db
```

### 2. Start Development
```bash
# Start everything (server + Electron)
npm run dev
```

**Expected Output:**
```
🚀 TiniApp server running on http://localhost:3000
📁 Serving frontend from: /path/to/tini/src/frontend
🔒 Authentication endpoints available at: /api/auth/*
🏥 Health check available at: /api/health
```

### 3. Test the Application
- Electron app should open automatically
- Visit http://localhost:3000 in browser
- Try creating a user account
- Test theme switching

## 📋 Detailed Development Setup

### Step 1: Environment Verification
```bash
# Verify all prerequisites
node --version                    # v22.14.0
bun --version                     # v1.2.15+
npm --version                     # 10.9.2+

# Check NODE_MODULE_VERSION compatibility
node -e "console.log(process.versions.modules)"  # Must be 127
```

### Step 2: Dependency Installation
```bash
# Clean install (if needed)
rm -rf node_modules bun.lockb package-lock.json
npm cache clean --force
bun pm cache rm

# Install dependencies
bun install

# Verify better-sqlite3 compilation
npm rebuild better-sqlite3
```

### Step 3: Database Setup
```bash
# Initialize database with Better Auth schema
npm run setup:db

# Verify database creation
ls -la data/app.db  # Should exist with proper size
```

### Step 4: Development Server
```bash
# Option 1: Start everything
npm run dev

# Option 2: Start components separately
npm run dev:server    # Terminal 1: Backend server
npm run dev:electron  # Terminal 2: Electron app
```

## 🏗️ Production Build Process

### Build All Components
```bash
# Clean build
npm run clean

# Build everything
npm run build

# Verify build output
ls -la dist/
# Should contain: server/, electron/, frontend/
```

### Individual Build Commands
```bash
npm run build:server    # TypeScript → JavaScript (ES modules)
npm run build:electron  # TypeScript → CommonJS (.cjs files)
npm run build:preload   # Preload script compilation
npm run build:frontend  # Copy static assets
```

### Start Production
```bash
# Terminal 1: Start production server
npm run start:server

# Terminal 2: Start Electron app
npm run start
```

## 🧪 Testing & Validation

### API Testing
```bash
# Health check
curl http://localhost:3000/api/health

# Session check
curl http://localhost:3000/api/auth/get-session

# User registration
curl -X POST http://localhost:3000/api/auth/sign-up/email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","name":"Test User"}'
```

### Frontend Testing
1. Open http://localhost:3000/test-integration.html
2. Click "Run All Tests"
3. Verify all tests pass
4. Check authentication flow

### Electron Testing
1. Ensure server is running
2. Launch Electron: `npm run start`
3. Test all UI components
4. Verify theme switching
5. Test authentication flow

## 🔧 Troubleshooting

### Common Issues & Solutions

#### 1. NODE_MODULE_VERSION Mismatch
**Error**: `better-sqlite3 was compiled against different Node.js version`

**Solution**:
```bash
# Switch to Node.js 22.x
nvm use 22
npm rebuild better-sqlite3
```

#### 2. Port 3000 Already in Use
**Error**: `EADDRINUSE: address already in use :::3000`

**Solution**:
```bash
# Kill processes using port 3000
lsof -ti:3000 | xargs kill -9
```

#### 3. Electron App Won't Start
**Error**: `exports is not defined in ES module scope`

**Solution**: Ensure Electron files are built as CommonJS (.cjs)
```bash
npm run build:electron
npm run build:preload
```

#### 4. Database Connection Issues
**Error**: `Cannot open database`

**Solution**:
```bash
# Recreate database
rm -f data/app.db
npm run setup:db
```

#### 5. Authentication Not Working
**Error**: API timeouts or validation errors

**Solution**:
```bash
# Check server logs
# Verify request body format
# Test with curl commands above
```

## 📦 Distribution & Packaging

### Create Distributable Packages
```bash
# Build and package for current platform
npm run dist

# Package only (no build)
npm run pack
```

### Platform-Specific Builds
```bash
# macOS
npm run dist -- --mac

# Windows
npm run dist -- --win

# Linux
npm run dist -- --linux
```

## 🎨 Customization Guide

### Adding New Themes
1. Edit `src/frontend/scripts/themes.js`
2. Add theme name to `availableThemes` array
3. Theme will appear in selector automatically

### Adding New API Endpoints
1. Add route handler in `src/server/index.ts`
2. Update `handleApiRequest` function
3. Test with curl or frontend

### Modifying UI Components
1. Edit HTML in `src/frontend/index.html`
2. Update styles in `src/frontend/styles/main.css`
3. Add JavaScript in `src/frontend/scripts/app.js`

## 🔐 Security Considerations

### Implemented Security Features
- ✅ Content Security Policy (CSP)
- ✅ CORS headers for cross-origin requests
- ✅ Rate limiting on API endpoints
- ✅ Input validation and sanitization
- ✅ Secure headers (X-Frame-Options, etc.)
- ✅ Context isolation in Electron
- ✅ Password hashing with Better Auth
- ✅ Session management with secure cookies

### Security Best Practices
1. Keep dependencies updated
2. Use HTTPS in production
3. Implement proper error handling
4. Log security events
5. Regular security audits

## 📊 Performance Optimization

### Development Performance
- Hot reload enabled for rapid development
- TypeScript compilation optimized
- Bun used for fast package management

### Production Performance
- Minified JavaScript and CSS
- Optimized static asset serving
- Efficient database queries
- Memory management in Electron

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request

### Code Standards
- TypeScript for type safety
- ESLint for code quality
- Prettier for formatting
- Conventional commits

## 📞 Support

### Getting Help
1. Check this documentation
2. Review troubleshooting section
3. Check GitHub issues
4. Create new issue with details

### Reporting Issues
Include:
- Operating system and version
- Node.js and Bun versions
- Error messages and logs
- Steps to reproduce

---

## ✅ Success Checklist

After following this guide, you should have:

- [ ] Node.js 22.x installed and verified
- [ ] Bun installed and working
- [ ] Dependencies installed successfully
- [ ] Database initialized with tables
- [ ] Development server running
- [ ] Electron app launching
- [ ] Authentication working
- [ ] All tests passing
- [ ] Production build working

**🎉 Congratulations! TiniApp is now fully operational!**
