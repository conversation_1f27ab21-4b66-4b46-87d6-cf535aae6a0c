{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Modern desktop application with Bun, DaisyUI, Better Auth, and Electron", "main": "dist/main.js", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:electron\"", "dev:server": "tsx watch src/server/index.ts", "dev:electron": "wait-on http://localhost:3000 && electron dist/main.js", "build": "npm run build:server && npm run build:electron", "build:server": "tsc src/server/index.ts --outDir dist --target es2022 --module commonjs", "build:electron": "bun build src/electron/main.ts --outdir dist --target node --external electron", "build:preload": "bun build src/electron/preload.ts --outdir dist --target node --external electron", "start": "electron dist/main.js", "pack": "electron-builder", "dist": "bun run build && electron-builder", "postinstall": "electron-builder install-app-deps", "setup:db": "tsx src/scripts/setup-db.ts", "setup:db:bun": "bun run src/scripts/setup-db.ts", "test": "bun test"}, "keywords": ["electron", "bun", "daisyui", "better-auth", "desktop", "typescript"], "author": "TiniApp Team", "license": "MIT", "devDependencies": {"@types/node": "^20.11.0", "concurrently": "^8.2.2", "electron": "^28.2.0", "electron-builder": "^24.9.1", "tailwindcss": "^3.4.1", "tsx": "^4.19.4", "typescript": "^5.3.3", "wait-on": "^7.2.0"}, "dependencies": {"better-auth": "^1.0.1", "better-sqlite3": "^11.10.0", "daisyui": "^4.6.0"}, "build": {"appId": "com.tiniapp.desktop", "productName": "TiniApp", "directories": {"output": "release"}, "files": ["dist/**/*", "src/frontend/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}