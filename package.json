{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Modern desktop application with Bun, DaisyUI, Better Auth, and Electron", "main": "dist/electron/main.cjs", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:electron\"", "dev:server": "tsx watch src/server/index.ts", "dev:electron": "wait-on http://localhost:3000 && electron dist/main.js", "build": "npm run clean && npm run build:server && npm run build:electron && npm run build:preload && npm run build:frontend", "build:server": "tsc src/server/index.ts --outDir dist/server --target es2022 --module es2022 --moduleResolution node --allowSyntheticDefaultImports --esModuleInterop --skipLibCheck --noEmitOnError false", "build:electron": "tsc src/electron/main.ts --outDir dist/electron --target es2022 --module commonjs --moduleResolution node --allowSyntheticDefaultImports --esModuleInterop --skipLibCheck --noEmitOnError false", "build:preload": "tsc src/electron/preload.ts --outDir dist/electron --target es2022 --module commonjs --moduleResolution node --allowSyntheticDefaultImports --esModuleInterop --skipLibCheck --noEmitOnError false", "build:frontend": "cp -r src/frontend dist/", "clean": "rm -rf dist", "start": "electron dist/electron/main.cjs", "start:server": "node dist/server/index.js", "pack": "electron-builder", "dist": "bun run build && electron-builder", "postinstall": "electron-builder install-app-deps", "setup:db": "tsx src/scripts/setup-db.ts", "setup:db:bun": "bun run src/scripts/setup-db.ts", "test": "bun test"}, "keywords": ["electron", "bun", "daisyui", "better-auth", "desktop", "typescript"], "author": "TiniApp Team", "license": "MIT", "devDependencies": {"@types/node": "^20.11.0", "concurrently": "^8.2.2", "electron": "^28.2.0", "electron-builder": "^24.9.1", "tailwindcss": "^3.4.1", "tsx": "^4.19.4", "typescript": "^5.3.3", "wait-on": "^7.2.0"}, "dependencies": {"better-auth": "^1.0.1", "better-sqlite3": "^11.10.0", "daisyui": "^4.6.0"}, "build": {"appId": "com.tiniapp.desktop", "productName": "TiniApp", "directories": {"output": "release"}, "files": ["dist/**/*", "src/frontend/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}