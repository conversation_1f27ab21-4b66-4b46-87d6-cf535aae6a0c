#!/usr/bin/env node

/**
 * TiniApp End-to-End Integration Test
 * 
 * This script performs comprehensive testing of:
 * - Server startup and health
 * - Database connectivity
 * - Authentication endpoints
 * - Frontend-backend communication
 * - Build system validation
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class TiniAppTester {
    constructor() {
        this.baseUrl = 'http://localhost:3000';
        this.testResults = [];
        this.serverProcess = null;
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            'info': '🔍',
            'success': '✅',
            'error': '❌',
            'warning': '⚠️'
        }[type] || 'ℹ️';
        
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async makeRequest(path, options = {}) {
        return new Promise((resolve, reject) => {
            const url = `${this.baseUrl}${path}`;
            const requestOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            };

            const req = http.request(url, requestOptions, (res) => {
                let data = '';
                res.on('data', chunk => data += chunk);
                res.on('end', () => {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        body: data
                    });
                });
            });

            req.on('error', reject);
            
            if (options.body) {
                req.write(options.body);
            }
            
            req.end();
        });
    }

    async testEnvironment() {
        this.log('Testing environment prerequisites...', 'info');
        
        try {
            // Check Node.js version
            const nodeVersion = process.version;
            const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
            
            if (majorVersion >= 22) {
                this.log(`Node.js version: ${nodeVersion} ✓`, 'success');
            } else {
                this.log(`Node.js version: ${nodeVersion} (requires v22+)`, 'error');
                return false;
            }

            // Check NODE_MODULE_VERSION
            const moduleVersion = process.versions.modules;
            if (moduleVersion === '127') {
                this.log(`NODE_MODULE_VERSION: ${moduleVersion} ✓`, 'success');
            } else {
                this.log(`NODE_MODULE_VERSION: ${moduleVersion} (requires 127)`, 'error');
                return false;
            }

            // Check database file
            const dbPath = path.join(process.cwd(), 'data', 'app.db');
            if (fs.existsSync(dbPath)) {
                this.log('Database file exists ✓', 'success');
            } else {
                this.log('Database file missing (run: npm run setup:db)', 'error');
                return false;
            }

            return true;
        } catch (error) {
            this.log(`Environment test failed: ${error.message}`, 'error');
            return false;
        }
    }

    async startServer() {
        this.log('Starting production server...', 'info');
        
        return new Promise((resolve, reject) => {
            this.serverProcess = spawn('node', ['dist/server/index.js'], {
                stdio: ['pipe', 'pipe', 'pipe'],
                env: { ...process.env, NODE_ENV: 'production' }
            });

            let serverReady = false;

            this.serverProcess.stdout.on('data', (data) => {
                const output = data.toString();
                if (output.includes('TiniApp server running')) {
                    serverReady = true;
                    this.log('Server started successfully', 'success');
                    resolve();
                }
            });

            this.serverProcess.stderr.on('data', (data) => {
                this.log(`Server error: ${data.toString()}`, 'error');
            });

            this.serverProcess.on('error', (error) => {
                this.log(`Failed to start server: ${error.message}`, 'error');
                reject(error);
            });

            // Timeout after 10 seconds
            setTimeout(() => {
                if (!serverReady) {
                    this.log('Server startup timeout', 'error');
                    reject(new Error('Server startup timeout'));
                }
            }, 10000);
        });
    }

    async testHealthEndpoint() {
        this.log('Testing health endpoint...', 'info');
        
        try {
            const response = await this.makeRequest('/api/health');
            
            if (response.status === 200) {
                const data = JSON.parse(response.body);
                if (data.status === 'ok') {
                    this.log('Health endpoint working ✓', 'success');
                    return true;
                } else {
                    this.log(`Health endpoint returned: ${data.status}`, 'error');
                    return false;
                }
            } else {
                this.log(`Health endpoint returned status: ${response.status}`, 'error');
                return false;
            }
        } catch (error) {
            this.log(`Health endpoint test failed: ${error.message}`, 'error');
            return false;
        }
    }

    async testAuthEndpoints() {
        this.log('Testing authentication endpoints...', 'info');
        
        try {
            // Test session endpoint
            const sessionResponse = await this.makeRequest('/api/auth/get-session');
            if (sessionResponse.status === 200) {
                this.log('Session endpoint working ✓', 'success');
            } else {
                this.log(`Session endpoint failed: ${sessionResponse.status}`, 'error');
                return false;
            }

            // Test user registration
            const testUser = {
                email: `test-${Date.now()}@example.com`,
                password: 'testpassword123',
                name: 'Test User'
            };

            const signupResponse = await this.makeRequest('/api/auth/sign-up/email', {
                method: 'POST',
                body: JSON.stringify(testUser)
            });

            if (signupResponse.status === 200) {
                const signupData = JSON.parse(signupResponse.body);
                if (signupData.user && signupData.user.email === testUser.email) {
                    this.log('User registration working ✓', 'success');
                } else {
                    this.log('User registration response invalid', 'error');
                    return false;
                }
            } else {
                this.log(`User registration failed: ${signupResponse.status}`, 'error');
                this.log(`Response: ${signupResponse.body}`, 'error');
                return false;
            }

            // Test user login
            const signinResponse = await this.makeRequest('/api/auth/sign-in/email', {
                method: 'POST',
                body: JSON.stringify({
                    email: testUser.email,
                    password: testUser.password
                })
            });

            if (signinResponse.status === 200) {
                const signinData = JSON.parse(signinResponse.body);
                if (signinData.user && signinData.user.email === testUser.email) {
                    this.log('User login working ✓', 'success');
                } else {
                    this.log('User login response invalid', 'error');
                    return false;
                }
            } else {
                this.log(`User login failed: ${signinResponse.status}`, 'error');
                return false;
            }

            return true;
        } catch (error) {
            this.log(`Authentication test failed: ${error.message}`, 'error');
            return false;
        }
    }

    async testStaticFiles() {
        this.log('Testing static file serving...', 'info');
        
        try {
            // Test main HTML file
            const htmlResponse = await this.makeRequest('/');
            if (htmlResponse.status === 200 && htmlResponse.body.includes('<!DOCTYPE html>')) {
                this.log('HTML serving working ✓', 'success');
            } else {
                this.log('HTML serving failed', 'error');
                return false;
            }

            // Test JavaScript file
            const jsResponse = await this.makeRequest('/scripts/app.js');
            if (jsResponse.status === 200) {
                this.log('JavaScript serving working ✓', 'success');
            } else {
                this.log('JavaScript serving failed', 'error');
                return false;
            }

            // Test CSS file
            const cssResponse = await this.makeRequest('/styles/main.css');
            if (cssResponse.status === 200) {
                this.log('CSS serving working ✓', 'success');
            } else {
                this.log('CSS serving failed', 'error');
                return false;
            }

            return true;
        } catch (error) {
            this.log(`Static file test failed: ${error.message}`, 'error');
            return false;
        }
    }

    async testBuildSystem() {
        this.log('Testing build system...', 'info');
        
        try {
            // Check if build directory exists
            const distPath = path.join(process.cwd(), 'dist');
            if (!fs.existsSync(distPath)) {
                this.log('Build directory missing (run: npm run build)', 'error');
                return false;
            }

            // Check server build
            const serverPath = path.join(distPath, 'server', 'index.js');
            if (fs.existsSync(serverPath)) {
                this.log('Server build exists ✓', 'success');
            } else {
                this.log('Server build missing', 'error');
                return false;
            }

            // Check Electron build
            const electronPath = path.join(distPath, 'electron', 'main.cjs');
            if (fs.existsSync(electronPath)) {
                this.log('Electron build exists ✓', 'success');
            } else {
                this.log('Electron build missing', 'error');
                return false;
            }

            // Check frontend build
            const frontendPath = path.join(distPath, 'frontend', 'index.html');
            if (fs.existsSync(frontendPath)) {
                this.log('Frontend build exists ✓', 'success');
            } else {
                this.log('Frontend build missing', 'error');
                return false;
            }

            return true;
        } catch (error) {
            this.log(`Build system test failed: ${error.message}`, 'error');
            return false;
        }
    }

    async cleanup() {
        if (this.serverProcess) {
            this.log('Stopping server...', 'info');
            this.serverProcess.kill('SIGTERM');
            await this.sleep(2000);
        }
    }

    async runAllTests() {
        this.log('🚀 Starting TiniApp End-to-End Tests', 'info');
        this.log('=====================================', 'info');

        const tests = [
            { name: 'Environment', fn: () => this.testEnvironment() },
            { name: 'Build System', fn: () => this.testBuildSystem() },
            { name: 'Server Startup', fn: () => this.startServer() },
            { name: 'Health Endpoint', fn: () => this.testHealthEndpoint() },
            { name: 'Authentication', fn: () => this.testAuthEndpoints() },
            { name: 'Static Files', fn: () => this.testStaticFiles() }
        ];

        let passed = 0;
        let failed = 0;

        for (const test of tests) {
            try {
                this.log(`\n🧪 Running ${test.name} test...`, 'info');
                const result = await test.fn();
                
                if (result) {
                    passed++;
                    this.log(`${test.name} test PASSED`, 'success');
                } else {
                    failed++;
                    this.log(`${test.name} test FAILED`, 'error');
                }
            } catch (error) {
                failed++;
                this.log(`${test.name} test ERROR: ${error.message}`, 'error');
            }
        }

        await this.cleanup();

        this.log('\n📊 Test Results Summary', 'info');
        this.log('======================', 'info');
        this.log(`✅ Passed: ${passed}`, 'success');
        this.log(`❌ Failed: ${failed}`, failed > 0 ? 'error' : 'info');
        this.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`, 'info');

        if (failed === 0) {
            this.log('\n🎉 All tests passed! TiniApp is fully operational!', 'success');
            process.exit(0);
        } else {
            this.log('\n💥 Some tests failed. Check the logs above for details.', 'error');
            process.exit(1);
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    const tester = new TiniAppTester();
    tester.runAllTests().catch(error => {
        console.error('Test runner failed:', error);
        process.exit(1);
    });
}

module.exports = TiniAppTester;
