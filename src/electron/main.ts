import { app, BrowserWindow, Menu, shell, ipc<PERSON>ain, dialog } from "electron";
import { join } from "path";
import { existsSync } from "fs";

const isDev = process.env.NODE_ENV === "development";
const SERVER_URL = "http://localhost:3000";

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null;

// Enable live reload for Electron in development
if (isDev) {
  try {
    require("electron-reload")(__dirname, {
      electron: join(__dirname, "..", "node_modules", ".bin", "electron"),
      hardResetMethod: "exit",
    });
  } catch (error) {
    console.log("electron-reload not available");
  }
}

function createWindow(): void {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false, // Don't show until ready
    icon: join(__dirname, "..", "frontend", "assets", "icon.png"),
    titleBarStyle: process.platform === "darwin" ? "hiddenInset" : "default",
    webPreferences: {
      nodeIntegration: false, // Security: disable node integration
      contextIsolation: true, // Security: enable context isolation
      // enableRemoteModule: false, // Security: disable remote module (deprecated)
      preload: join(__dirname, "preload.js"), // Preload script
      webSecurity: true, // Enable web security
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
    },
  });

  // Load the app
  if (isDev) {
    // In development, load from the dev server
    mainWindow.loadURL(SERVER_URL);
    
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load from the built files
    const indexPath = join(__dirname, "..", "frontend", "index.html");
    if (existsSync(indexPath)) {
      mainWindow.loadFile(indexPath);
    } else {
      // Fallback to server URL
      mainWindow.loadURL(SERVER_URL);
    }
  }

  // Show window when ready to prevent visual flash
  mainWindow.once("ready-to-show", () => {
    if (mainWindow) {
      mainWindow.show();
      
      // Focus on window
      if (isDev) {
        mainWindow.focus();
      }
    }
  });

  // Handle window closed
  mainWindow.on("closed", () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: "deny" };
  });

  // Prevent navigation to external URLs
  mainWindow.webContents.on("will-navigate", (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== SERVER_URL && !navigationUrl.startsWith("file://")) {
      event.preventDefault();
      shell.openExternal(navigationUrl);
    }
  });

  // Handle certificate errors
  mainWindow.webContents.on("certificate-error", (event, url, error, certificate, callback) => {
    if (isDev && url.startsWith("https://localhost")) {
      // In development, ignore certificate errors for localhost
      event.preventDefault();
      callback(true);
    } else {
      // In production, use default behavior
      callback(false);
    }
  });
}

// Create application menu
function createMenu(): void {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: "File",
      submenu: [
        {
          label: "New",
          accelerator: "CmdOrCtrl+N",
          click: () => {
            // Handle new file/document
            mainWindow?.webContents.send("menu-new");
          },
        },
        { type: "separator" },
        {
          label: "Quit",
          accelerator: process.platform === "darwin" ? "Cmd+Q" : "Ctrl+Q",
          click: () => {
            app.quit();
          },
        },
      ],
    },
    {
      label: "Edit",
      submenu: [
        { role: "undo" },
        { role: "redo" },
        { type: "separator" },
        { role: "cut" },
        { role: "copy" },
        { role: "paste" },
        { role: "selectAll" },
      ],
    },
    {
      label: "View",
      submenu: [
        { role: "reload" },
        { role: "forceReload" },
        { role: "toggleDevTools" },
        { type: "separator" },
        { role: "resetZoom" },
        { role: "zoomIn" },
        { role: "zoomOut" },
        { type: "separator" },
        { role: "togglefullscreen" },
      ],
    },
    {
      label: "Window",
      submenu: [
        { role: "minimize" },
        { role: "close" },
      ],
    },
    {
      label: "Help",
      submenu: [
        {
          label: "About TiniApp",
          click: () => {
            dialog.showMessageBox(mainWindow!, {
              type: "info",
              title: "About TiniApp",
              message: "TiniApp",
              detail: "A modern desktop application built with Bun, DaisyUI, Better Auth, and Electron.",
              buttons: ["OK"],
            });
          },
        },
        {
          label: "Learn More",
          click: () => {
            shell.openExternal("https://github.com/your-repo/tiniapp");
          },
        },
      ],
    },
  ];

  // macOS specific menu adjustments
  if (process.platform === "darwin") {
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: "about" },
        { type: "separator" },
        { role: "services" },
        { type: "separator" },
        { role: "hide" },
        { role: "hideOthers" },
        { role: "unhide" },
        { type: "separator" },
        { role: "quit" },
      ],
    });

    // Window menu
    template[4].submenu = [
      { role: "close" },
      { role: "minimize" },
      { role: "zoom" },
      { type: "separator" },
      { role: "front" },
    ];
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(() => {
  createWindow();
  createMenu();

  app.on("activate", () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed
app.on("window-all-closed", () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// Security: Prevent new window creation
app.on("web-contents-created", (event, contents) => {
  contents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
});

// IPC handlers
ipcMain.handle("app-version", () => {
  return app.getVersion();
});

ipcMain.handle("app-name", () => {
  return app.getName();
});

ipcMain.handle("show-message-box", async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow!, options);
  return result;
});

// Handle app protocol for deep linking (optional)
if (process.defaultApp) {
  if (process.argv.length >= 2) {
    app.setAsDefaultProtocolClient("tiniapp", process.execPath, [
      join(__dirname, "..", "..", ".."),
    ]);
  }
} else {
  app.setAsDefaultProtocolClient("tiniapp");
}

// Single instance lock
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on("second-instance", (event, commandLine, workingDirectory) => {
    // Someone tried to run a second instance, focus our window instead
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
}
