import { auth } from "./auth";
import { join } from "path";
import { existsSync, readFileSync } from "fs";
import { createServer } from "http";
import { URL } from "url";

const PORT = process.env.PORT || 3000;
const isDev = process.env.NODE_ENV === "development";

// CORS headers for Electron
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, Cookie",
  "Access-Control-Allow-Credentials": "true",
};

// Security headers
const securityHeaders = {
  "X-Content-Type-Options": "nosniff",
  "X-Frame-Options": "DENY",
  "X-XSS-Protection": "1; mode=block",
  "Referrer-Policy": "strict-origin-when-cross-origin",
  ...corsHeaders,
};

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Rate limiting middleware
function rateLimit(req: Request): Response | null {
  const ip = req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "127.0.0.1";
  const key = `rate_limit_${ip}`;
  const now = Date.now();
  const windowMs = 15 * 60 * 1000; // 15 minutes
  const maxRequests = 100;

  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return null;
  }

  if (current.count >= maxRequests) {
    return new Response(
      JSON.stringify({ error: "Too many requests" }),
      {
        status: 429,
        headers: {
          ...securityHeaders,
          "Content-Type": "application/json",
          "Retry-After": Math.ceil((current.resetTime - now) / 1000).toString(),
        },
      }
    );
  }

  current.count++;
  return null;
}

// Static file serving
function serveStatic(pathname: string): { content: Buffer; contentType: string; headers: Record<string, string> } | null {
  const frontendDir = join(process.cwd(), "src", "frontend");
  let filePath: string;

  if (pathname === "/" || pathname === "/index.html") {
    filePath = join(frontendDir, "index.html");
  } else {
    filePath = join(frontendDir, pathname);
  }

  // Security: prevent directory traversal
  if (!filePath.startsWith(frontendDir)) {
    return null;
  }

  if (!existsSync(filePath)) {
    return null;
  }

  try {
    const content = readFileSync(filePath);
    const ext = filePath.split(".").pop()?.toLowerCase();

    let contentType = "text/plain";
    switch (ext) {
      case "html":
        contentType = "text/html";
        break;
      case "css":
        contentType = "text/css";
        break;
      case "js":
        contentType = "application/javascript";
        break;
      case "json":
        contentType = "application/json";
        break;
      case "png":
        contentType = "image/png";
        break;
      case "jpg":
      case "jpeg":
        contentType = "image/jpeg";
        break;
      case "svg":
        contentType = "image/svg+xml";
        break;
      case "ico":
        contentType = "image/x-icon";
        break;
    }

    return {
      content,
      contentType,
      headers: {
        ...securityHeaders,
        "Content-Type": contentType,
        "Cache-Control": isDev ? "no-cache" : "public, max-age=31536000",
      }
    };
  } catch (error) {
    console.error("Error serving static file:", error);
    return null;
  }
}

// Helper function to parse request body
async function parseRequestBody(req: any): Promise<any> {
  return new Promise((resolve, reject) => {
    let body = '';
    const timeout = setTimeout(() => {
      reject(new Error('Request body parsing timeout'));
    }, 5000); // 5 second timeout

    req.on('data', (chunk: any) => {
      body += chunk.toString();
    });
    req.on('end', () => {
      clearTimeout(timeout);
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
    req.on('error', (error: any) => {
      clearTimeout(timeout);
      reject(error);
    });
  });
}

// Convert Node.js request to Web API Request
function createWebRequest(req: any, body?: string): Request {
  const url = `http://localhost:${PORT}${req.url}`;
  const headers = new Headers();

  for (const [key, value] of Object.entries(req.headers)) {
    if (typeof value === 'string') {
      headers.set(key, value);
    } else if (Array.isArray(value)) {
      headers.set(key, value.join(', '));
    }
  }

  return new Request(url, {
    method: req.method,
    headers,
    body: body,
  });
}

// API routes
async function handleApiRequest(req: any, pathname: string): Promise<{ status: number; headers: Record<string, string>; body: string }> {
  // Handle auth routes
  if (pathname.startsWith("/api/auth")) {
    console.log(`🔐 Auth request: ${req.method} ${pathname}`);
    try {
      let body: string | undefined;
      if (req.method !== 'GET' && req.method !== 'HEAD') {
        try {
          const bodyData = await parseRequestBody(req);
          body = JSON.stringify(bodyData);
          console.log(`📝 Request body:`, bodyData);
        } catch (error) {
          console.error('Error parsing request body:', error);
        }
      }

      const webReq = createWebRequest(req, body);
      console.log(`📤 Calling auth.handler for ${pathname}`);
      const response = await auth.handler(webReq);
      const responseBody = await response.text();
      console.log(`📥 Auth response: ${response.status}`);

      const headers: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headers[key] = value;
      });

      return {
        status: response.status,
        headers: { ...securityHeaders, ...headers },
        body: responseBody
      };
    } catch (error) {
      console.error("Auth error:", error);
      return {
        status: 500,
        headers: {
          ...securityHeaders,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ error: "Authentication error" })
      };
    }
  }

  // Health check endpoint
  if (pathname === "/api/health") {
    return {
      status: 200,
      headers: {
        ...securityHeaders,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        status: "ok",
        timestamp: new Date().toISOString(),
        version: process.env.APP_VERSION || "1.0.0",
      })
    };
  }

  // Protected API routes
  if (pathname.startsWith("/api/")) {
    const webReq = await createWebRequest(req);
    const session = await auth.api.getSession({ headers: webReq.headers });

    if (!session) {
      return {
        status: 401,
        headers: {
          ...securityHeaders,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ error: "Unauthorized" })
      };
    }

    // User profile endpoint
    if (pathname === "/api/user/profile" && req.method === "GET") {
      return {
        status: 200,
        headers: {
          ...securityHeaders,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ user: session.user })
      };
    }

    // Update user profile
    if (pathname === "/api/user/profile" && req.method === "PUT") {
      try {
        const body = await parseRequestBody(req);
        // Here you would update the user profile
        // For now, just return success
        return {
          status: 200,
          headers: {
            ...securityHeaders,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ success: true, message: "Profile updated" })
        };
      } catch (error) {
        return {
          status: 400,
          headers: {
            ...securityHeaders,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ error: "Invalid request body" })
        };
      }
    }
  }

  return {
    status: 404,
    headers: securityHeaders,
    body: "Not Found"
  };
}

// Main server
const server = createServer(async (req, res) => {
  try {
    const url = new URL(req.url!, `http://localhost:${PORT}`);
    const pathname = url.pathname;

    // Handle OPTIONS requests for CORS
    if (req.method === "OPTIONS") {
      res.writeHead(200, corsHeaders);
      res.end();
      return;
    }

    // Apply rate limiting
    const webReq = await createWebRequest(req);
    const rateLimitResponse = rateLimit(webReq);
    if (rateLimitResponse) {
      const rateLimitData = await rateLimitResponse.text();
      res.writeHead(rateLimitResponse.status, rateLimitResponse.headers as any);
      res.end(rateLimitData);
      return;
    }

    // Handle API requests
    if (pathname.startsWith("/api/")) {
      const apiResponse = await handleApiRequest(req, pathname);
      res.writeHead(apiResponse.status, apiResponse.headers);
      res.end(apiResponse.body);
      return;
    }

    // Serve static files
    const staticResponse = serveStatic(pathname);
    if (staticResponse) {
      res.writeHead(200, staticResponse.headers);
      res.end(staticResponse.content);
      return;
    }

    // Fallback to index.html for SPA routing
    const indexResponse = serveStatic("/index.html");
    if (indexResponse) {
      res.writeHead(200, indexResponse.headers);
      res.end(indexResponse.content);
    } else {
      res.writeHead(404, securityHeaders);
      res.end("Not Found");
    }
  } catch (error) {
    console.error("Server error:", error);
    res.writeHead(500, securityHeaders);
    res.end("Internal Server Error");
  }
});

// Start the server
server.listen(PORT, () => {
  console.log(`🚀 TiniApp server running on http://localhost:${PORT}`);
  console.log(`📁 Serving frontend from: ${join(process.cwd(), "src", "frontend")}`);
  console.log(`🔒 Authentication endpoints available at: /api/auth/*`);
  console.log(`🏥 Health check available at: /api/health`);
});

// Graceful shutdown
process.on("SIGINT", () => {
  console.log("\n🛑 Shutting down server...");
  server.close(() => {
    process.exit(0);
  });
});

process.on("SIGTERM", () => {
  console.log("\n🛑 Shutting down server...");
  server.close(() => {
    process.exit(0);
  });
});
