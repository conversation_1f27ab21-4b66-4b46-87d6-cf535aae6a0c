/**
 * Authentication Module
 * Handles user authentication, session management, and API communication
 */

class AuthManager {
    constructor() {
        this.baseURL = window.location.origin;
        this.currentUser = null;
        this.isAuthenticated = false;
        this.sessionCheckInterval = null;
        
        // Initialize authentication state
        this.init();
    }

    async init() {
        try {
            // Check if user is already authenticated
            await this.checkSession();
            
            // Start session monitoring
            this.startSessionMonitoring();
        } catch (error) {
            console.error('Auth initialization failed:', error);
        }
    }

    /**
     * Check current session status
     */
    async checkSession() {
        try {
            const response = await this.apiCall('/api/auth/get-session', {
                method: 'GET',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.session && data.user) {
                    this.currentUser = data.user;
                    this.isAuthenticated = true;
                    this.updateUI(true);
                    return true;
                }
            }
            
            this.currentUser = null;
            this.isAuthenticated = false;
            this.updateUI(false);
            return false;
        } catch (error) {
            console.error('Session check failed:', error);
            this.currentUser = null;
            this.isAuthenticated = false;
            this.updateUI(false);
            return false;
        }
    }

    /**
     * Sign in user
     */
    async signIn(email, password) {
        try {
            const response = await this.apiCall('/api/auth/sign-in/email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    email: email.trim(),
                    password: password
                })
            });

            const data = await response.json();

            if (response.ok && data.user) {
                this.currentUser = data.user;
                this.isAuthenticated = true;
                this.updateUI(true);
                
                // Show success message
                showToast('Welcome back!', 'success');
                
                // Close login modal
                document.getElementById('login-modal').close();
                
                return { success: true, user: data.user };
            } else {
                const errorMessage = data.error || 'Login failed. Please check your credentials.';
                showToast(errorMessage, 'error');
                return { success: false, error: errorMessage };
            }
        } catch (error) {
            console.error('Sign in error:', error);
            const errorMessage = 'Network error. Please try again.';
            showToast(errorMessage, 'error');
            return { success: false, error: errorMessage };
        }
    }

    /**
     * Sign up new user
     */
    async signUp(userData) {
        try {
            const response = await this.apiCall('/api/auth/sign-up/email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify({
                    email: userData.email.trim(),
                    password: userData.password,
                    name: `${userData.firstName} ${userData.lastName}`.trim(),
                    firstName: userData.firstName,
                    lastName: userData.lastName
                })
            });

            const data = await response.json();

            if (response.ok && data.user) {
                // Don't auto-login after registration - redirect to login instead

                // Show success message
                showToast('Account created successfully! Please sign in with your new account.', 'success');

                // Close register modal
                document.getElementById('register-modal').close();

                // Show login modal after a brief delay
                setTimeout(() => {
                    document.getElementById('login-modal').showModal();
                    // Pre-fill email in login form
                    const loginEmailInput = document.getElementById('login-email');
                    if (loginEmailInput) {
                        loginEmailInput.value = userData.email.trim();
                    }
                }, 500);

                return { success: true, user: data.user, redirectToLogin: true };
            } else {
                const errorMessage = data.error || 'Registration failed. Please try again.';
                showToast(errorMessage, 'error');
                return { success: false, error: errorMessage };
            }
        } catch (error) {
            console.error('Sign up error:', error);
            const errorMessage = 'Network error. Please try again.';
            showToast(errorMessage, 'error');
            return { success: false, error: errorMessage };
        }
    }

    /**
     * Sign out user
     */
    async signOut() {
        try {
            const response = await this.apiCall('/api/auth/sign-out', {
                method: 'POST',
                credentials: 'include'
            });

            // Clear local state regardless of response
            this.currentUser = null;
            this.isAuthenticated = false;
            this.updateUI(false);
            
            // Stop session monitoring
            this.stopSessionMonitoring();
            
            showToast('You have been signed out successfully.', 'info');
            
            return { success: true };
        } catch (error) {
            console.error('Sign out error:', error);
            
            // Still clear local state on error
            this.currentUser = null;
            this.isAuthenticated = false;
            this.updateUI(false);
            
            showToast('Signed out locally.', 'warning');
            return { success: false, error: error.message };
        }
    }

    /**
     * Update user profile
     */
    async updateProfile(profileData) {
        try {
            const response = await this.apiCall('/api/user/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify(profileData)
            });

            const data = await response.json();

            if (response.ok) {
                // Update local user data
                this.currentUser = { ...this.currentUser, ...profileData };
                this.updateUI(true);
                
                showToast('Profile updated successfully!', 'success');
                return { success: true, user: this.currentUser };
            } else {
                const errorMessage = data.error || 'Failed to update profile.';
                showToast(errorMessage, 'error');
                return { success: false, error: errorMessage };
            }
        } catch (error) {
            console.error('Profile update error:', error);
            const errorMessage = 'Network error. Please try again.';
            showToast(errorMessage, 'error');
            return { success: false, error: errorMessage };
        }
    }

    /**
     * Make authenticated API calls
     */
    async apiCall(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        
        const defaultOptions = {
            credentials: 'include',
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, finalOptions);
            
            // Handle authentication errors
            if (response.status === 401) {
                this.currentUser = null;
                this.isAuthenticated = false;
                this.updateUI(false);
                showToast('Session expired. Please sign in again.', 'warning');
            }
            
            return response;
        } catch (error) {
            console.error('API call failed:', error);
            throw error;
        }
    }

    /**
     * Start monitoring session status
     */
    startSessionMonitoring() {
        // Check session every 5 minutes
        this.sessionCheckInterval = setInterval(() => {
            this.checkSession();
        }, 5 * 60 * 1000);
    }

    /**
     * Stop monitoring session status
     */
    stopSessionMonitoring() {
        if (this.sessionCheckInterval) {
            clearInterval(this.sessionCheckInterval);
            this.sessionCheckInterval = null;
        }
    }

    /**
     * Update UI based on authentication status
     */
    updateUI(isAuthenticated) {
        const loginBtn = document.getElementById('login-btn');
        const userMenu = document.getElementById('user-menu');
        const welcomeSection = document.getElementById('welcome-section');
        const dashboardSection = document.getElementById('dashboard-section');
        const userAvatar = document.getElementById('user-avatar');

        if (isAuthenticated && this.currentUser) {
            // Hide login button, show user menu
            loginBtn.style.display = 'none';
            userMenu.style.display = 'block';
            
            // Hide welcome, show dashboard
            welcomeSection.classList.add('hidden');
            dashboardSection.classList.remove('hidden');
            
            // Update user avatar
            if (this.currentUser.avatar) {
                userAvatar.src = this.currentUser.avatar;
            } else {
                // Generate avatar based on initials
                const initials = this.getInitials(this.currentUser.name || this.currentUser.email);
                userAvatar.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=random`;
            }
            
            // Update user info in UI
            this.updateUserInfo();
        } else {
            // Show login button, hide user menu
            loginBtn.style.display = 'block';
            userMenu.style.display = 'none';
            
            // Show welcome, hide dashboard
            welcomeSection.classList.remove('hidden');
            dashboardSection.classList.add('hidden');
        }
    }

    /**
     * Update user information in the UI
     */
    updateUserInfo() {
        if (!this.currentUser) return;

        // Update any user-specific content in the dashboard
        const userElements = document.querySelectorAll('[data-user-field]');
        userElements.forEach(element => {
            const field = element.getAttribute('data-user-field');
            if (this.currentUser[field]) {
                element.textContent = this.currentUser[field];
            }
        });
    }

    /**
     * Get user initials for avatar
     */
    getInitials(name) {
        if (!name) return 'U';
        
        const parts = name.split(' ');
        if (parts.length >= 2) {
            return (parts[0][0] + parts[1][0]).toUpperCase();
        }
        return name[0].toUpperCase();
    }

    /**
     * Validate email format
     */
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validate password strength
     */
    validatePassword(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        
        return {
            isValid: password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers,
            length: password.length >= minLength,
            upperCase: hasUpperCase,
            lowerCase: hasLowerCase,
            numbers: hasNumbers
        };
    }
}

// Initialize auth manager
const authManager = new AuthManager();

// Export for global access
window.authManager = authManager;
