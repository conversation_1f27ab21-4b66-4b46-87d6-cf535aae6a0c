/**
 * UI Management Module
 * Handles UI interactions, theme management, and user interface updates
 */

class UIManager {
    constructor() {
        this.currentTheme = 'light';
        this.toastContainer = null;
        this.init();
    }

    init() {
        // Initialize theme
        this.initTheme();
        
        // Initialize toast container
        this.initToastContainer();
        
        // Initialize modals
        this.initModals();
        
        // Initialize form handlers
        this.initFormHandlers();
        
        // Initialize navigation
        this.initNavigation();
        
        // Initialize Electron API if available
        this.initElectronAPI();
    }

    /**
     * Initialize theme management
     */
    initTheme() {
        // Get saved theme or use system preference
        const savedTheme = localStorage.getItem('app-theme');
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        
        this.currentTheme = savedTheme || systemTheme;
        this.setTheme(this.currentTheme);
        
        // Listen for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('app-theme')) {
                this.setTheme(e.matches ? 'dark' : 'light');
            }
        });
    }

    /**
     * Set application theme
     */
    setTheme(theme) {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('app-theme', theme);
        
        // Update Electron API if available
        if (window.electronAPI) {
            window.electronAPI.setTheme(theme);
        }
        
        // Dispatch theme change event
        window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    /**
     * Initialize toast notification system
     */
    initToastContainer() {
        this.toastContainer = document.getElementById('toast-container');
        if (!this.toastContainer) {
            this.toastContainer = document.createElement('div');
            this.toastContainer.id = 'toast-container';
            this.toastContainer.className = 'toast toast-top toast-end z-50';
            document.body.appendChild(this.toastContainer);
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info', duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} shadow-lg animate-fade-in`;
        
        const icon = this.getToastIcon(type);
        toast.innerHTML = `
            <div class="flex items-center">
                ${icon}
                <span>${message}</span>
                <button class="btn btn-sm btn-ghost ml-auto" onclick="this.parentElement.parentElement.remove()">
                    ✕
                </button>
            </div>
        `;
        
        this.toastContainer.appendChild(toast);
        
        // Auto remove after duration
        setTimeout(() => {
            if (toast.parentElement) {
                toast.classList.add('animate-fade-out');
                setTimeout(() => toast.remove(), 300);
            }
        }, duration);
        
        return toast;
    }

    /**
     * Get icon for toast type
     */
    getToastIcon(type) {
        const icons = {
            success: '<svg class="w-6 h-6 text-success" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
            error: '<svg class="w-6 h-6 text-error" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
            warning: '<svg class="w-6 h-6 text-warning" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
            info: '<svg class="w-6 h-6 text-info" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
        };
        return icons[type] || icons.info;
    }

    /**
     * Initialize modal functionality
     */
    initModals() {
        // Close modals when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.close();
            }
        });
        
        // Close modals with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModals = document.querySelectorAll('dialog[open]');
                openModals.forEach(modal => modal.close());
            }
        });
    }

    /**
     * Initialize form handlers
     */
    initFormHandlers() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLoginSubmit.bind(this));
        }
        
        // Register form
        const registerForm = document.getElementById('register-form');
        if (registerForm) {
            registerForm.addEventListener('submit', this.handleRegisterSubmit.bind(this));
        }
        
        // Add real-time validation
        this.initFormValidation();
    }

    /**
     * Handle login form submission
     */
    async handleLoginSubmit(e) {
        e.preventDefault();
        
        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const loadingSpinner = document.getElementById('login-loading');
        
        // Validate inputs
        if (!email || !password) {
            this.showToast('Please fill in all fields', 'error');
            return;
        }
        
        if (!authManager.validateEmail(email)) {
            this.showToast('Please enter a valid email address', 'error');
            return;
        }
        
        // Show loading state
        this.setLoadingState(submitBtn, loadingSpinner, true);
        
        try {
            const result = await authManager.signIn(email, password);
            
            if (result.success) {
                // Clear form
                e.target.reset();
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showToast('An unexpected error occurred', 'error');
        } finally {
            this.setLoadingState(submitBtn, loadingSpinner, false);
        }
    }

    /**
     * Handle register form submission
     */
    async handleRegisterSubmit(e) {
        e.preventDefault();
        
        const firstName = document.getElementById('register-firstname').value;
        const lastName = document.getElementById('register-lastname').value;
        const email = document.getElementById('register-email').value;
        const password = document.getElementById('register-password').value;
        const confirmPassword = document.getElementById('register-confirm-password').value;
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const loadingSpinner = document.getElementById('register-loading');
        
        // Validate inputs
        if (!email || !password || !confirmPassword) {
            this.showToast('Please fill in all required fields', 'error');
            return;
        }
        
        if (!authManager.validateEmail(email)) {
            this.showToast('Please enter a valid email address', 'error');
            return;
        }
        
        if (password !== confirmPassword) {
            this.showToast('Passwords do not match', 'error');
            return;
        }
        
        const passwordValidation = authManager.validatePassword(password);
        if (!passwordValidation.isValid) {
            this.showToast('Password must be at least 8 characters with uppercase, lowercase, and numbers', 'error');
            return;
        }
        
        // Show loading state
        this.setLoadingState(submitBtn, loadingSpinner, true);
        
        try {
            const userData = {
                firstName: firstName.trim(),
                lastName: lastName.trim(),
                email: email.trim(),
                password: password
            };
            
            const result = await authManager.signUp(userData);

            if (result.success) {
                // Clear form
                e.target.reset();

                // If redirecting to login, show additional guidance
                if (result.redirectToLogin) {
                    // Form is already cleared, user will be redirected to login
                    console.log('User registration successful, redirecting to login');
                }
            }
        } catch (error) {
            console.error('Registration error:', error);
            this.showToast('An unexpected error occurred', 'error');
        } finally {
            this.setLoadingState(submitBtn, loadingSpinner, false);
        }
    }

    /**
     * Initialize form validation
     */
    initFormValidation() {
        // Email validation
        const emailInputs = document.querySelectorAll('input[type="email"]');
        emailInputs.forEach(input => {
            input.addEventListener('blur', (e) => {
                const email = e.target.value.trim();
                if (email && !authManager.validateEmail(email)) {
                    e.target.classList.add('input-error');
                    this.showFieldError(e.target, 'Please enter a valid email address');
                } else {
                    e.target.classList.remove('input-error');
                    this.hideFieldError(e.target);
                }
            });
        });
        
        // Password validation
        const passwordInputs = document.querySelectorAll('input[type="password"]');
        passwordInputs.forEach(input => {
            if (input.id.includes('register-password') && !input.id.includes('confirm')) {
                input.addEventListener('input', (e) => {
                    const password = e.target.value;
                    const validation = authManager.validatePassword(password);
                    
                    if (password && !validation.isValid) {
                        e.target.classList.add('input-warning');
                        this.showPasswordStrength(e.target, validation);
                    } else {
                        e.target.classList.remove('input-warning');
                        this.hideFieldError(e.target);
                    }
                });
            }
        });
        
        // Confirm password validation
        const confirmPasswordInput = document.getElementById('register-confirm-password');
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('blur', (e) => {
                const password = document.getElementById('register-password').value;
                const confirmPassword = e.target.value;
                
                if (confirmPassword && password !== confirmPassword) {
                    e.target.classList.add('input-error');
                    this.showFieldError(e.target, 'Passwords do not match');
                } else {
                    e.target.classList.remove('input-error');
                    this.hideFieldError(e.target);
                }
            });
        }
    }

    /**
     * Show field error message
     */
    showFieldError(field, message) {
        this.hideFieldError(field); // Remove existing error
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'text-error text-sm mt-1 field-error';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }

    /**
     * Hide field error message
     */
    hideFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }

    /**
     * Show password strength indicator
     */
    showPasswordStrength(field, validation) {
        this.hideFieldError(field);
        
        const strengthDiv = document.createElement('div');
        strengthDiv.className = 'text-sm mt-1 field-error';
        
        const requirements = [
            { met: validation.length, text: 'At least 8 characters' },
            { met: validation.upperCase, text: 'One uppercase letter' },
            { met: validation.lowerCase, text: 'One lowercase letter' },
            { met: validation.numbers, text: 'One number' }
        ];
        
        const requirementsList = requirements.map(req => 
            `<span class="${req.met ? 'text-success' : 'text-error'}">${req.met ? '✓' : '✗'} ${req.text}</span>`
        ).join('<br>');
        
        strengthDiv.innerHTML = requirementsList;
        field.parentNode.appendChild(strengthDiv);
    }

    /**
     * Set loading state for buttons
     */
    setLoadingState(button, spinner, isLoading) {
        if (isLoading) {
            button.disabled = true;
            if (spinner) spinner.classList.remove('hidden');
        } else {
            button.disabled = false;
            if (spinner) spinner.classList.add('hidden');
        }
    }

    /**
     * Initialize navigation
     */
    initNavigation() {
        // Handle navigation clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('nav-link')) {
                e.preventDefault();
                const href = e.target.getAttribute('href');
                if (href && href.startsWith('#')) {
                    this.navigateTo(href.substring(1));
                }
            }
        });
    }

    /**
     * Navigate to section
     */
    navigateTo(section) {
        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[href="#${section}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
        
        // Handle section-specific logic
        switch (section) {
            case 'dashboard':
                // Already handled by auth state
                break;
            case 'profile':
                this.showProfileSection();
                break;
            case 'settings':
                this.showSettingsSection();
                break;
        }
    }

    /**
     * Show profile section
     */
    showProfileSection() {
        this.showToast('Profile section coming soon!', 'info');
    }

    /**
     * Show settings section
     */
    showSettingsSection() {
        this.showToast('Settings section coming soon!', 'info');
    }

    /**
     * Initialize Electron API integration
     */
    initElectronAPI() {
        if (window.electronAPI) {
            // Handle menu events
            window.electronAPI.onMenuNew(() => {
                this.showToast('New action triggered from menu', 'info');
            });
            
            // Apply saved theme
            const savedTheme = window.electronAPI.getTheme();
            if (savedTheme) {
                this.setTheme(savedTheme);
            }
        }
    }

    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');
        
        if (loadingScreen && app) {
            loadingScreen.classList.add('animate-fade-out');
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                app.classList.remove('hidden');
                app.classList.add('animate-fade-in');
            }, 500);
        }
    }
}

// Initialize UI manager
const uiManager = new UIManager();

// Global functions for HTML onclick handlers
window.setTheme = (theme) => uiManager.setTheme(theme);
window.showToast = (message, type, duration) => uiManager.showToast(message, type, duration);
window.showLoginModal = () => document.getElementById('login-modal').showModal();
window.showRegisterModal = () => {
    document.getElementById('login-modal').close();
    document.getElementById('register-modal').showModal();
};
window.logout = () => authManager.signOut();

// Export for global access
window.uiManager = uiManager;
