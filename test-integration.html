<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TiniApp Integration Testing</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.6.0/dist/full.min.css" rel="stylesheet" type="text/css" />
</head>
<body class="bg-base-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">TiniApp Integration Testing</h1>
        
        <!-- Test Results Display -->
        <div id="test-results" class="mb-8">
            <h2 class="text-2xl font-semibold mb-4">Test Results</h2>
            <div id="results-container" class="space-y-2"></div>
        </div>
        
        <!-- API Testing Section -->
        <div class="card bg-base-200 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title">API Endpoint Testing</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button id="test-session" class="btn btn-primary">Test Get Session</button>
                    <button id="test-signup" class="btn btn-secondary">Test Sign Up</button>
                    <button id="test-signin" class="btn btn-accent">Test Sign In</button>
                    <button id="test-signout" class="btn btn-warning">Test Sign Out</button>
                    <button id="test-health" class="btn btn-info">Test Health Check</button>
                    <button id="run-all-tests" class="btn btn-success">Run All Tests</button>
                </div>
            </div>
        </div>
        
        <!-- User Registration Form -->
        <div class="card bg-base-200 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title">User Registration Test</h2>
                <form id="signup-form" class="space-y-4">
                    <div class="form-control">
                        <label class="label"><span class="label-text">Email</span></label>
                        <input type="email" id="signup-email" class="input input-bordered" value="<EMAIL>" required>
                    </div>
                    <div class="form-control">
                        <label class="label"><span class="label-text">Password</span></label>
                        <input type="password" id="signup-password" class="input input-bordered" value="password123" required>
                    </div>
                    <div class="form-control">
                        <label class="label"><span class="label-text">Name</span></label>
                        <input type="text" id="signup-name" class="input input-bordered" value="Test User" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Register User</button>
                </form>
            </div>
        </div>
        
        <!-- User Login Form -->
        <div class="card bg-base-200 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title">User Login Test</h2>
                <form id="signin-form" class="space-y-4">
                    <div class="form-control">
                        <label class="label"><span class="label-text">Email</span></label>
                        <input type="email" id="signin-email" class="input input-bordered" value="<EMAIL>" required>
                    </div>
                    <div class="form-control">
                        <label class="label"><span class="label-text">Password</span></label>
                        <input type="password" id="signin-password" class="input input-bordered" value="password123" required>
                    </div>
                    <button type="submit" class="btn btn-accent">Login User</button>
                </form>
            </div>
        </div>
        
        <!-- Session Info Display -->
        <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
                <h2 class="card-title">Current Session</h2>
                <div id="session-info" class="text-sm">
                    <p>No session data available</p>
                </div>
                <button id="refresh-session" class="btn btn-outline">Refresh Session</button>
            </div>
        </div>
    </div>

    <script>
        // Test utilities
        function addTestResult(test, status, message, data = null) {
            const container = document.getElementById('results-container');
            const result = document.createElement('div');
            result.className = `alert ${status === 'success' ? 'alert-success' : status === 'error' ? 'alert-error' : 'alert-info'}`;
            result.innerHTML = `
                <div>
                    <strong>${test}:</strong> ${message}
                    ${data ? `<pre class="text-xs mt-2">${JSON.stringify(data, null, 2)}</pre>` : ''}
                </div>
            `;
            container.appendChild(result);
            container.scrollTop = container.scrollHeight;
        }

        // API testing functions
        async function testGetSession() {
            try {
                const response = await fetch('/api/auth/get-session', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                addTestResult('Get Session', 'success', `Status: ${response.status}`, data);
                updateSessionInfo(data);
                return { success: true, data };
            } catch (error) {
                addTestResult('Get Session', 'error', error.message);
                return { success: false, error: error.message };
            }
        }

        async function testSignUp() {
            try {
                const email = document.getElementById('signup-email').value;
                const password = document.getElementById('signup-password').value;
                const name = document.getElementById('signup-name').value;
                
                const response = await fetch('/api/auth/sign-up/email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password, name })
                });
                
                const data = await response.json();
                addTestResult('Sign Up', response.ok ? 'success' : 'error', `Status: ${response.status}`, data);
                
                if (response.ok) {
                    await testGetSession(); // Refresh session after signup
                }
                
                return { success: response.ok, data };
            } catch (error) {
                addTestResult('Sign Up', 'error', error.message);
                return { success: false, error: error.message };
            }
        }

        async function testSignIn() {
            try {
                const email = document.getElementById('signin-email').value;
                const password = document.getElementById('signin-password').value;
                
                const response = await fetch('/api/auth/sign-in/email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                addTestResult('Sign In', response.ok ? 'success' : 'error', `Status: ${response.status}`, data);
                
                if (response.ok) {
                    await testGetSession(); // Refresh session after signin
                }
                
                return { success: response.ok, data };
            } catch (error) {
                addTestResult('Sign In', 'error', error.message);
                return { success: false, error: error.message };
            }
        }

        async function testSignOut() {
            try {
                const response = await fetch('/api/auth/sign-out', {
                    method: 'POST',
                    credentials: 'include'
                });
                
                const data = await response.text();
                addTestResult('Sign Out', 'success', `Status: ${response.status}`, data);
                
                await testGetSession(); // Refresh session after signout
                return { success: true, data };
            } catch (error) {
                addTestResult('Sign Out', 'error', error.message);
                return { success: false, error: error.message };
            }
        }

        async function testHealthCheck() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                addTestResult('Health Check', 'success', `Status: ${response.status}`, data);
                return { success: true, data };
            } catch (error) {
                addTestResult('Health Check', 'error', error.message);
                return { success: false, error: error.message };
            }
        }

        function updateSessionInfo(sessionData) {
            const sessionInfo = document.getElementById('session-info');
            if (sessionData && sessionData.user) {
                sessionInfo.innerHTML = `
                    <p><strong>User ID:</strong> ${sessionData.user.id}</p>
                    <p><strong>Email:</strong> ${sessionData.user.email}</p>
                    <p><strong>Name:</strong> ${sessionData.user.name || 'N/A'}</p>
                    <p><strong>Created:</strong> ${new Date(sessionData.user.createdAt).toLocaleString()}</p>
                `;
            } else {
                sessionInfo.innerHTML = '<p>No active session</p>';
            }
        }

        async function runAllTests() {
            addTestResult('Test Suite', 'info', 'Starting comprehensive API testing...');
            
            // Test health check first
            await testHealthCheck();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Test session (should be null initially)
            await testGetSession();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Test sign up
            await testSignUp();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Test sign out
            await testSignOut();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Test sign in
            await testSignIn();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            addTestResult('Test Suite', 'success', 'All tests completed!');
        }

        // Event listeners
        document.getElementById('test-session').addEventListener('click', testGetSession);
        document.getElementById('test-signup').addEventListener('click', testSignUp);
        document.getElementById('test-signin').addEventListener('click', testSignIn);
        document.getElementById('test-signout').addEventListener('click', testSignOut);
        document.getElementById('test-health').addEventListener('click', testHealthCheck);
        document.getElementById('run-all-tests').addEventListener('click', runAllTests);
        document.getElementById('refresh-session').addEventListener('click', testGetSession);

        document.getElementById('signup-form').addEventListener('submit', (e) => {
            e.preventDefault();
            testSignUp();
        });

        document.getElementById('signin-form').addEventListener('submit', (e) => {
            e.preventDefault();
            testSignIn();
        });

        // Initialize with session check
        window.addEventListener('load', () => {
            testGetSession();
        });
    </script>
</body>
</html>
