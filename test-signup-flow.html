<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TiniApp Sign-Up Flow Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.6.0/dist/full.min.css" rel="stylesheet" type="text/css" />
</head>
<body class="bg-base-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">TiniApp Sign-Up Flow Testing</h1>
        
        <!-- Test Results Display -->
        <div id="test-results" class="mb-8">
            <h2 class="text-2xl font-semibold mb-4">Test Results</h2>
            <div id="results-container" class="space-y-2"></div>
        </div>
        
        <!-- API Integration Tests -->
        <div class="card bg-base-200 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title">1. API Integration Verification</h2>
                <p class="text-sm text-base-content/70 mb-4">
                    Testing the /api/auth/sign-up/email endpoint with proper headers and payload
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button id="test-api-headers" class="btn btn-primary">Test API Headers</button>
                    <button id="test-api-payload" class="btn btn-secondary">Test API Payload</button>
                    <button id="test-api-credentials" class="btn btn-accent">Test Credentials</button>
                    <button id="test-api-response" class="btn btn-info">Test Response Format</button>
                </div>
            </div>
        </div>
        
        <!-- Success Flow Tests -->
        <div class="card bg-base-200 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title">2. Success Flow Implementation</h2>
                <p class="text-sm text-base-content/70 mb-4">
                    Testing user flow after successful registration
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button id="test-success-redirect" class="btn btn-success">Test Login Redirect</button>
                    <button id="test-success-toast" class="btn btn-warning">Test Success Toast</button>
                    <button id="test-form-clear" class="btn btn-error">Test Form Clearing</button>
                    <button id="test-email-prefill" class="btn btn-neutral">Test Email Pre-fill</button>
                </div>
            </div>
        </div>
        
        <!-- Error Handling Tests -->
        <div class="card bg-base-200 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title">3. Error Handling</h2>
                <p class="text-sm text-base-content/70 mb-4">
                    Testing error scenarios and user feedback
                </p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button id="test-duplicate-email" class="btn btn-outline btn-error">Test Duplicate Email</button>
                    <button id="test-validation-errors" class="btn btn-outline btn-warning">Test Validation</button>
                    <button id="test-network-error" class="btn btn-outline btn-info">Test Network Error</button>
                    <button id="test-server-error" class="btn btn-outline btn-secondary">Test Server Error</button>
                </div>
            </div>
        </div>
        
        <!-- Complete Flow Test -->
        <div class="card bg-base-200 shadow-xl mb-8">
            <div class="card-body">
                <h2 class="card-title">4. Complete Flow Test</h2>
                <p class="text-sm text-base-content/70 mb-4">
                    End-to-end testing: Sign-up → Redirect → Login → Success
                </p>
                
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Test Email</span>
                    </label>
                    <input type="email" id="test-email" class="input input-bordered" value="<EMAIL>" />
                </div>
                
                <div class="form-control mb-4">
                    <label class="label">
                        <span class="label-text">Test Password</span>
                    </label>
                    <input type="password" id="test-password" class="input input-bordered" value="FlowTest123" />
                </div>
                
                <button id="run-complete-flow" class="btn btn-primary btn-lg">
                    Run Complete Flow Test
                </button>
            </div>
        </div>
        
        <!-- Live API Testing -->
        <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
                <h2 class="card-title">5. Live API Testing</h2>
                <p class="text-sm text-base-content/70 mb-4">
                    Test actual API calls to verify backend integration
                </p>
                
                <div class="space-y-4">
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Email</span>
                        </label>
                        <input type="email" id="live-email" class="input input-bordered" placeholder="<EMAIL>" />
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Password</span>
                        </label>
                        <input type="password" id="live-password" class="input input-bordered" placeholder="Password123" />
                    </div>
                    
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">Name</span>
                        </label>
                        <input type="text" id="live-name" class="input input-bordered" placeholder="Test User" />
                    </div>
                    
                    <button id="test-live-signup" class="btn btn-success">
                        Test Live Sign-Up
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test utilities
        function addTestResult(test, status, message, data = null) {
            const container = document.getElementById('results-container');
            const result = document.createElement('div');
            result.className = `alert ${status === 'success' ? 'alert-success' : status === 'error' ? 'alert-error' : 'alert-info'}`;
            result.innerHTML = `
                <div>
                    <strong>${test}:</strong> ${message}
                    ${data ? `<pre class="text-xs mt-2">${JSON.stringify(data, null, 2)}</pre>` : ''}
                </div>
            `;
            container.appendChild(result);
            container.scrollTop = container.scrollHeight;
        }

        // API Integration Tests
        document.getElementById('test-api-headers').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/auth/sign-up/email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'HeaderTest123',
                        name: 'Header Test'
                    })
                });
                
                addTestResult('API Headers', 'success', 'Content-Type and credentials properly set', {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries())
                });
            } catch (error) {
                addTestResult('API Headers', 'error', error.message);
            }
        });

        document.getElementById('test-api-payload').addEventListener('click', async () => {
            const testPayload = {
                email: '<EMAIL>',
                password: 'PayloadTest123',
                name: 'Payload Test'
            };
            
            try {
                const response = await fetch('/api/auth/sign-up/email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify(testPayload)
                });
                
                const data = await response.json();
                
                if (response.ok && data.user) {
                    addTestResult('API Payload', 'success', 'Payload correctly formatted and processed', {
                        sent: testPayload,
                        received: data.user
                    });
                } else {
                    addTestResult('API Payload', 'error', 'Payload processing failed', data);
                }
            } catch (error) {
                addTestResult('API Payload', 'error', error.message);
            }
        });

        document.getElementById('test-api-credentials').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/auth/sign-up/email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'CredentialsTest123',
                        name: 'Credentials Test'
                    })
                });
                
                // Check if cookies are being set
                const cookies = document.cookie;
                addTestResult('API Credentials', 'success', 'Credentials include working', {
                    status: response.status,
                    cookiesSet: cookies.length > 0,
                    cookies: cookies
                });
            } catch (error) {
                addTestResult('API Credentials', 'error', error.message);
            }
        });

        document.getElementById('test-api-response').addEventListener('click', async () => {
            try {
                const response = await fetch('/api/auth/sign-up/email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'ResponseTest123',
                        name: 'Response Test'
                    })
                });
                
                const data = await response.json();
                
                const hasRequiredFields = data.user && 
                    data.user.id && 
                    data.user.email && 
                    data.user.name;
                
                addTestResult('API Response', hasRequiredFields ? 'success' : 'error', 
                    hasRequiredFields ? 'Response format correct' : 'Missing required fields', 
                    data);
            } catch (error) {
                addTestResult('API Response', 'error', error.message);
            }
        });

        // Success Flow Tests
        document.getElementById('test-success-redirect').addEventListener('click', () => {
            addTestResult('Success Redirect', 'info', 'This test requires manual verification in the main app');
        });

        document.getElementById('test-success-toast').addEventListener('click', () => {
            addTestResult('Success Toast', 'info', 'This test requires manual verification in the main app');
        });

        document.getElementById('test-form-clear').addEventListener('click', () => {
            addTestResult('Form Clear', 'info', 'This test requires manual verification in the main app');
        });

        document.getElementById('test-email-prefill').addEventListener('click', () => {
            addTestResult('Email Pre-fill', 'info', 'This test requires manual verification in the main app');
        });

        // Error Handling Tests
        document.getElementById('test-duplicate-email').addEventListener('click', async () => {
            const duplicateEmail = '<EMAIL>';
            
            try {
                // First registration
                await fetch('/api/auth/sign-up/email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: duplicateEmail,
                        password: 'DuplicateTest123',
                        name: 'Duplicate Test'
                    })
                });
                
                // Second registration (should fail)
                const response = await fetch('/api/auth/sign-up/email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: duplicateEmail,
                        password: 'DuplicateTest456',
                        name: 'Duplicate Test 2'
                    })
                });
                
                const data = await response.json();
                
                if (!response.ok) {
                    addTestResult('Duplicate Email', 'success', 'Duplicate email properly rejected', data);
                } else {
                    addTestResult('Duplicate Email', 'error', 'Duplicate email was accepted', data);
                }
            } catch (error) {
                addTestResult('Duplicate Email', 'error', error.message);
            }
        });

        // Live API Testing
        document.getElementById('test-live-signup').addEventListener('click', async () => {
            const email = document.getElementById('live-email').value;
            const password = document.getElementById('live-password').value;
            const name = document.getElementById('live-name').value;
            
            if (!email || !password || !name) {
                addTestResult('Live Sign-Up', 'error', 'Please fill in all fields');
                return;
            }
            
            try {
                const response = await fetch('/api/auth/sign-up/email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({ email, password, name })
                });
                
                const data = await response.json();
                
                if (response.ok && data.user) {
                    addTestResult('Live Sign-Up', 'success', 'User created successfully', data.user);
                    
                    // Clear form
                    document.getElementById('live-email').value = '';
                    document.getElementById('live-password').value = '';
                    document.getElementById('live-name').value = '';
                } else {
                    addTestResult('Live Sign-Up', 'error', 'Sign-up failed', data);
                }
            } catch (error) {
                addTestResult('Live Sign-Up', 'error', error.message);
            }
        });

        // Complete Flow Test
        document.getElementById('run-complete-flow').addEventListener('click', async () => {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            
            addTestResult('Complete Flow', 'info', 'Starting complete flow test...');
            
            try {
                // Step 1: Sign up
                const signupResponse = await fetch('/api/auth/sign-up/email', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        name: 'Flow Test User'
                    })
                });
                
                const signupData = await signupResponse.json();
                
                if (signupResponse.ok && signupData.user) {
                    addTestResult('Complete Flow - Sign Up', 'success', 'User registration successful', signupData.user);
                    
                    // Step 2: Try to login
                    const loginResponse = await fetch('/api/auth/sign-in/email', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        credentials: 'include',
                        body: JSON.stringify({
                            email: email,
                            password: password
                        })
                    });
                    
                    const loginData = await loginResponse.json();
                    
                    if (loginResponse.ok && loginData.user) {
                        addTestResult('Complete Flow - Login', 'success', 'User login successful', loginData.user);
                        addTestResult('Complete Flow', 'success', 'Complete flow test PASSED! ✅');
                    } else {
                        addTestResult('Complete Flow - Login', 'error', 'Login failed after registration', loginData);
                    }
                } else {
                    addTestResult('Complete Flow - Sign Up', 'error', 'Registration failed', signupData);
                }
            } catch (error) {
                addTestResult('Complete Flow', 'error', error.message);
            }
        });

        // Initialize
        addTestResult('Test Suite', 'info', 'TiniApp Sign-Up Flow Testing initialized. Click buttons to run tests.');
    </script>
</body>
</html>
